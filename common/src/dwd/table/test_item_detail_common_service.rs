use std::collections::HashMap;
use std::error::Error;

use crate::dto::dwd::file_detail::FileDetail;
use crate::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use crate::dto::dwd::test_item_detail_row::TestItemDetailRow;
use crate::dto::ods::test_item_data_parquet::TestItemDataParquet;
use crate::dwd::model::value::die_test_info::DieTestInfo;
use crate::dwd::table::dwd_table_service::DwdTableService;
use crate::dwd::util::dwd_common_util::DwdCommonUtil;
use crate::model::constant::*;
use crate::model::key::die_key::DieKey;
use crate::utils::date;

// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-common/src/main/scala/com/guwave/onedata/dataware/dw/common/dwd/table/TestItemDetailCommonService.scala

/// TestItemDetailCommonService provides common functionality for TestItemDetail processing
/// This is the base service class that contains shared methods for building and manipulating test item details
#[derive(Debug, Clone)]
pub struct TestItemDetailCommonService;

impl TestItemDetailCommonService {
    /// Creates a new instance of TestItemDetailCommonService
    pub fn new() -> Self {
        Self
    }

    /// Get standard units mapping for unit conversion
    ///
    /// Corresponds to: TestItemDetailCommonService.scala:29-40
    /// def getStandardUnits(needMultiplyScale: Boolean, standardUnits: String): Map[String, String]
    pub fn get_standard_units(
        &self,
        need_multiply_scale: bool,
        standard_units: &str,
    ) -> Result<HashMap<String, String>, Box<dyn Error>> {
        // Return empty map if needMultiplyScale is false
        // Corresponds to Scala getStandardUnits line 31
        if !need_multiply_scale {
            return Ok(HashMap::new());
        }

        // Parse standard units string into a map
        // Corresponds to Scala getStandardUnits lines 33-38
        let standard_units_map = standard_units
            .split(SEMICOLON)
            .map(|unit_pair| {
                let parts: Vec<&str> = unit_pair.split(COLON).collect();
                if parts.len() == 2 {
                    let key = parts[0].trim();
                    let value = parts[1].trim();
                    (key.to_string(), value.to_string())
                } else {
                    (String::new(), String::new())
                }
            })
            .collect::<HashMap<String, String>>();

        Ok(standard_units_map)
    }

    /// Build CP TestItemDetail from TestItemData
    ///
    /// Corresponds to: TestItemDetailCommonService.scala:52-90
    /// def buildCpTestItemDetail(testItemData: TestItemData, standardUnits: Map[String, String],
    ///                          fileCategory: String, needMultiplyScale: Boolean): SubTestItemDetail
    pub fn build_cp_test_item_detail(
        &self,
        test_item_data: &TestItemDataParquet,
        standard_units: &HashMap<String, String>,
        file_category: &str,
        need_multiply_scale: bool,
    ) -> Result<SubTestItemDetail, Box<dyn Error>> {
        // scala代码
        // val now = System.currentTimeMillis()
        // val createHourKey = DateUtil.getDayHour(now)
        // val createDayKey = DateUtil.getDay(now)
        let now = chrono::Utc::now();
        let create_hour_key = date::get_day_hour(now);
        let create_day_key = date::get_day(now);
        // val sbinPf = DwdCommonUtil.cleanBinPf(testItemData.sbinPf, testItemData.partFlg)
        let sbin_pf =
            DwdCommonUtil::clean_bin_pf(test_item_data.sbinPf.as_deref().unwrap(), test_item_data.partFlg.as_deref());
        // val hbinPf = DwdCommonUtil.cleanBinPf(testItemData.hbinPf, testItemData.partFlg)
        let hbin_pf =
            DwdCommonUtil::clean_bin_pf(test_item_data.hbinPf.as_deref().unwrap(), test_item_data.partFlg.as_deref());

        let mut sub_test_item_detail = SubTestItemDetail::new();
        // SubTestItemDetail(
        //  FILE_ID = testItemData.fileId,
        sub_test_item_detail.FILE_ID = test_item_data.fileId;
        //  ONLINE_RETEST = testItemData.onlineRetest,
        sub_test_item_detail.ONLINE_RETEST = test_item_data.onlineRetest;
        //  MAX_OFFLINE_RETEST = 0,
        sub_test_item_detail.MAX_OFFLINE_RETEST = Some(0);
        //  MAX_ONLINE_RETEST = 0,
        sub_test_item_detail.MAX_ONLINE_RETEST = Some(0);
        //  IS_DIE_FIRST_TEST = 0,
        sub_test_item_detail.IS_DIE_FINAL_TEST = Some(0);
        //  IS_FIRST_TEST = 0,
        sub_test_item_detail.IS_FIRST_TEST = Some(0);
        //  IS_FINAL_TEST = 0,
        sub_test_item_detail.IS_FINAL_TEST = Some(0);
        //  IS_FIRST_TEST_IGNORE_TP = 1,
        sub_test_item_detail.IS_FIRST_TEST_IGNORE_TP = Some(1);
        //  IS_FINAL_TEST_IGNORE_TP = 1,
        sub_test_item_detail.IS_FINAL_TEST_IGNORE_TP = Some(1);
        //  IS_DUP_FIRST_TEST = 0,
        sub_test_item_detail.IS_DUP_FINAL_TEST = Some(0);
        //  IS_DUP_FIRST_TEST_IGNORE_TP = 0,
        sub_test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP = Some(0);
        //  IS_DUP_FINAL_TEST_IGNORE_TP = 0,
        sub_test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP = Some(0);
        //  TEST_SUITE = EMPTY,
        sub_test_item_detail.TEST_SUITE = Some(String::new());
        //  CONDITION_SET = if (testItemData.conditionSet != null) testItemData.conditionSet else Map(),
        if let Some(condition_set) = test_item_data.conditionSet.clone() {
            sub_test_item_detail.CONDITION_SET = Some(condition_set);
        }
        //  TEST_NUM = testItemData.testNum,
        sub_test_item_detail.TEST_NUM = test_item_data.testNum;
        //  TEST_TXT = testItemData.testTxt,
        sub_test_item_detail.TEST_TXT = test_item_data.testTxt.clone();
        //  TEST_ITEM = testItemData.testItem,
        sub_test_item_detail.TEST_ITEM = test_item_data.testItem.clone();
        //  IS_DIE_FIRST_TEST_ITEM = 0,
        sub_test_item_detail.IS_DIE_FIRST_TEST_ITEM = Some(0);
        //  TESTITEM_TYPE = testItemData.testitemType,
        sub_test_item_detail.TESTITEM_TYPE = test_item_data.testitemType.clone();
        //  TEST_FLG = testItemData.testFlg,
        sub_test_item_detail.TEST_FLG = test_item_data.testFlg.clone();
        //  PARM_FLG = testItemData.parmFlg,
        sub_test_item_detail.PARM_FLG = test_item_data.parmFlg.clone();
        //  TEST_STATE = calculateTestState(testItemData,fileCategory),
        sub_test_item_detail.TEST_STATE =
            Some(DwdTableService::calculate_test_state_file_category(test_item_data, file_category));
        //  TEST_VALUE = calculateTestValue(testItemData,fileCategory,needMultiplyScale),
        sub_test_item_detail.TEST_VALUE =
            DwdTableService::calculate_test_value(test_item_data, file_category, need_multiply_scale);
        //  UNITS = standardUnits.getOrElse(stringValue(calculateResScal(testItemData,fileCategory)),EMPTY) + stringValue(testItemData.units),
        let units = test_item_data.units.as_deref().unwrap_or("");
        let res_scal = DwdTableService::calculate_res_scal(test_item_data, file_category);
        let res_scal_units = standard_units
            .get(res_scal.unwrap_or(0).to_string().as_str())
            .map(|units| units.to_string())
            .unwrap_or(String::new());
        sub_test_item_detail.UNITS = Some(format!("{}{}", res_scal_units, units));
        //  TEST_RESULT = calculateTestResult(testItemData,fileCategory),
        sub_test_item_detail.TEST_RESULT =
            DwdTableService::calculate_test_result_file_category(test_item_data, file_category);
        //  ORIGIN_TEST_VALUE = null,
        sub_test_item_detail.ORIGIN_TEST_VALUE = None;
        //  ORIGIN_UNITS = testItemData.units,
        sub_test_item_detail.ORIGIN_UNITS = test_item_data.units.clone();
        //  TEST_ORDER = testItemData.testOrder,
        sub_test_item_detail.TEST_ORDER = test_item_data.testOrder;
        //  ALARM_ID = testItemData.alarmId,
        sub_test_item_detail.ALARM_ID = test_item_data.alarmId.clone();
        //  OPT_FLG = testItemData.optFlg,
        sub_test_item_detail.OPT_FLG = test_item_data.optFlg.clone();
        //  RES_SCAL = calculateResScal(testItemData,fileCategory),
        sub_test_item_detail.RES_SCAL = DwdTableService::calculate_res_scal(test_item_data, file_category);
        //  NUM_TEST = testItemData.numTest,
        sub_test_item_detail.NUM_TEST = test_item_data.numTest;
        //  LLM_SCAL = calculateLlmScal(testItemData,fileCategory),
        sub_test_item_detail.LLM_SCAL = DwdTableService::calculate_llm_scal(test_item_data, file_category);
        //  HLM_SCAL = calculateHlmScal(testItemData,fileCategory),
        sub_test_item_detail.HLM_SCAL = DwdTableService::calculate_hlm_scal(test_item_data, file_category);
        //  LO_LIMIT = calculateLoLimit(testItemData,fileCategory,needMultiplyScale),
        sub_test_item_detail.LO_LIMIT =
            DwdTableService::calculate_lo_limit(test_item_data, file_category, need_multiply_scale);
        //  HI_LIMIT = calculateHiLimit(testItemData,fileCategory,needMultiplyScale),
        sub_test_item_detail.HI_LIMIT =
            DwdTableService::calculate_hi_limit(test_item_data, file_category, need_multiply_scale);
        //  ORIGIN_HI_LIMIT = toBigDecimal(testItemData.hiLimit),
        sub_test_item_detail.ORIGIN_HI_LIMIT = test_item_data.hiLimit;
        //  ORIGIN_LO_LIMIT = toBigDecimal(testItemData.loLimit),
        sub_test_item_detail.ORIGIN_LO_LIMIT = test_item_data.loLimit;
        //  C_RESFMT = testItemData.cResfmt,
        sub_test_item_detail.C_RESFMT = test_item_data.cResfmt.clone();
        //  C_LLMFMT = testItemData.cLlmfmt,
        sub_test_item_detail.C_LLMFMT = test_item_data.cLlmfmt.clone();
        //  C_HLMFMT = testItemData.cHlmfmt,
        sub_test_item_detail.C_HLMFMT = test_item_data.cHlmfmt.clone();
        //  LO_SPEC = toBigDecimal(testItemData.loSpec),
        sub_test_item_detail.LO_SPEC = test_item_data.loSpec;
        //  HI_SPEC = toBigDecimal(testItemData.hiSpec),
        sub_test_item_detail.HI_SPEC = test_item_data.hiSpec;
        //  HBIN_NUM = testItemData.hbinNum,
        sub_test_item_detail.HBIN_NUM = test_item_data.hbinNum;
        //  SBIN_NUM = testItemData.sbinNum,
        sub_test_item_detail.SBIN_NUM = test_item_data.sbinNum;
        //  SBIN_PF = sbinPf,
        sub_test_item_detail.SBIN_PF = Some(sbin_pf);
        //  SBIN_NAM = testItemData.sbinNam,
        sub_test_item_detail.SBIN_NAM = test_item_data.sbinNam.clone();
        //  HBIN_PF = hbinPf,
        sub_test_item_detail.HBIN_PF = Some(hbin_pf);
        //  HBIN_NAM = testItemData.hbinNam,
        sub_test_item_detail.HBIN_NAM = test_item_data.hbinNam.clone();
        //  HBIN = buildBin(HBIN_NAME_PREFIX,testItemData.hbinNum,testItemData.hbinNam),
        sub_test_item_detail.HBIN = Some(DwdTableService::build_bin(
            HBIN_NAME_PREFIX,
            test_item_data.hbinNum,
            test_item_data.hbinNam.as_deref(),
        ));
        //  SBIN = buildBin(SBIN_NAME_PREFIX,testItemData.sbinNum,testItemData.sbinNam),
        sub_test_item_detail.SBIN = Some(DwdTableService::build_bin(
            SBIN_NAME_PREFIX,
            test_item_data.sbinNum,
            test_item_data.sbinNam.as_deref(),
        ));
        //  TEST_HEAD = testItemData.testHead,
        sub_test_item_detail.TEST_HEAD = test_item_data.testHead;
        //  PART_FLG = testItemData.partFlg,
        sub_test_item_detail.PART_FLG = test_item_data.partFlg.clone();
        //  PART_ID = testItemData.partId,
        sub_test_item_detail.PART_ID = test_item_data.partId.clone();
        //  C_PART_ID = testItemData.cPartId.longValue(),
        sub_test_item_detail.C_PART_ID = test_item_data.cPartId.map(|id| id as i64);
        //  ECID = EMPTY,
        sub_test_item_detail.ECID = Some(String::new());
        //  ECID_EXT = stringValue(testItemData.ecidExt),
        sub_test_item_detail.ECID_EXT = test_item_data.ecidExt.clone();
        //  ECID_EXTRA = if (testItemData.ecidExtra != null) testItemData.ecidExtra else Map(),
        if let Some(ecid_extra) = test_item_data.ecidExtra.clone() {
            sub_test_item_detail.ECID_EXTRA = Some(ecid_extra);
        }
        //  IS_STANDARD_ECID = 1,
        sub_test_item_detail.IS_STANDARD_ECID = Some(1);
        //  X_COORD = testItemData.xCoord,
        sub_test_item_detail.X_COORD = test_item_data.xCoord;
        //  Y_COORD = testItemData.yCoord,
        sub_test_item_detail.Y_COORD = test_item_data.yCoord;
        //  DIE_X = testItemData.xCoord,
        sub_test_item_detail.DIE_X = test_item_data.xCoord;
        //  DIE_Y = testItemData.yCoord,
        sub_test_item_detail.DIE_Y = test_item_data.yCoord;
        //  TEST_TIME = testItemData.testTime,
        sub_test_item_detail.TEST_TIME = test_item_data.testTime;
        //  PART_TXT = testItemData.partTxt,
        sub_test_item_detail.PART_TXT = test_item_data.partTxt.clone();
        //  PART_FIX = testItemData.partFix,
        sub_test_item_detail.PART_FIX = test_item_data.partFix.clone();
        //  SITE = testItemData.site,
        sub_test_item_detail.SITE = test_item_data.site;
        //  TOUCH_DOWN_ID = testItemData.touchDownId,
        sub_test_item_detail.TOUCH_DOWN_ID = test_item_data.touchDownId;
        //  WAFER_LOT_ID = EMPTY,
        sub_test_item_detail.WAFER_LOT_ID = Some(String::new());
        //  WAFER_ID = EMPTY,
        sub_test_item_detail.WAFER_ID = Some(String::new());
        //  WAFER_NO = EMPTY,
        sub_test_item_detail.WAFER_NO = Some(String::new());
        //  RETICLE_T_X = null,
        sub_test_item_detail.RETICLE_T_X = None;
        //  RETICLE_T_Y = null,
        sub_test_item_detail.RETICLE_T_Y = None;
        //  RETICLE_X = null,
        sub_test_item_detail.RETICLE_X = None;
        //  RETICLE_Y = null,
        sub_test_item_detail.RETICLE_Y = None;
        //  SITE_ID = EMPTY,
        sub_test_item_detail.SITE_ID = Some(String::new());
        //  VECT_NAM = testItemData.vectNam,
        sub_test_item_detail.VECT_NAM = test_item_data.vectNam.clone();
        //  TIME_SET = testItemData.timeSet,
        sub_test_item_detail.TIME_SET = test_item_data.timeSet.clone();
        //  NUM_FAIL = testItemData.numFail,
        sub_test_item_detail.NUM_FAIL = test_item_data.numFail;
        //  FAIL_PIN = testItemData.failPin,
        sub_test_item_detail.FAIL_PIN = test_item_data.failPin.clone();
        //  CYCL_CNT = testItemData.cyclCnt,
        sub_test_item_detail.CYCL_CNT = test_item_data.cyclCnt;
        //  REPT_CNT = testItemData.reptCnt,
        sub_test_item_detail.REPT_CNT = test_item_data.reptCnt;
        //  LONG_ATTRIBUTE_SET = Map(),
        sub_test_item_detail.LONG_ATTRIBUTE_SET = None;
        //  STRING_ATTRIBUTE_SET = Map(),
        sub_test_item_detail.STRING_ATTRIBUTE_SET = None;
        //  FLOAT_ATTRIBUTE_SET = Map(),
        sub_test_item_detail.FLOAT_ATTRIBUTE_SET = None;
        //  UID = EMPTY,
        sub_test_item_detail.UID = Some(String::new());
        //  TEXT_DAT = stringValue(testItemData.textDat),
        sub_test_item_detail.TEXT_DAT = test_item_data.textDat.clone();
        //  CREATE_HOUR_KEY = createHourKey,
        sub_test_item_detail.CREATE_HOUR_KEY = Some(create_hour_key);
        //  CREATE_DAY_KEY = createDayKey,
        sub_test_item_detail.CREATE_DAY_KEY = Some(create_day_key);
        //  CREATE_TIME = now,
        sub_test_item_detail.CREATE_TIME = now.timestamp_millis();
        //  EFUSE_EXTRA = if (testItemData.efuseExtra != null) testItemData.efuseExtra else Map(),
        if let Some(efuse_extra) = test_item_data.efuseExtra.clone() {
            sub_test_item_detail.EFUSE_EXTRA = Some(efuse_extra);
        }
        //  CHIP_ID = stringValue(testItemData.chipId))
        sub_test_item_detail.CHIP_ID = test_item_data.chipId.clone();
        Ok(sub_test_item_detail)
    }

    /// Build FT TestItemDetail from TestItemData
    ///
    /// Corresponds to: TestItemDetailCommonService.scala:101-140
    /// def buildFtTestItemDetail(testItemData: TestItemData, standardUnits: Map[String, String],
    ///                          fileCategory: String, needMultiplyScale: Boolean): SubTestItemDetail
    pub fn build_ft_test_item_detail(
        &self,
        test_item_data: &TestItemDataParquet,
        standard_units: &HashMap<String, String>,
        file_category: &str,
        need_multiply_scale: bool,
    ) -> Result<SubTestItemDetail, Box<dyn Error>> {
        // val now = System.currentTimeMillis()
        let now = chrono::Utc::now();
        // val createHourKey = DateUtil.getDayHour(now)
        let create_hour_key = date::get_day_hour(now);
        // val createDayKey = DateUtil.getDay(now)
        let create_day_key = date::get_day(now);
        // val sbinPf = DwdCommonUtil.cleanBinPf(testItemData.sbinPf, testItemData.partFlg)
        let sbin_pf =
            DwdCommonUtil::clean_bin_pf(test_item_data.sbinPf.as_deref().unwrap(), test_item_data.partFlg.as_deref());
        // val hbinPf = DwdCommonUtil.cleanBinPf(testItemData.hbinPf, testItemData.partFlg)
        let hbin_pf =
            DwdCommonUtil::clean_bin_pf(test_item_data.hbinPf.as_deref().unwrap(), test_item_data.partFlg.as_deref());
        let res_scal = DwdTableService::calculate_res_scal(test_item_data, file_category);

        // SubTestItemDetail(
        let mut sub_test_item_detail = SubTestItemDetail::new();
        //    FILE_ID = testItemData.fileId,
        sub_test_item_detail.FILE_ID = test_item_data.fileId;
        //    ONLINE_RETEST = testItemData.onlineRetest,
        sub_test_item_detail.ONLINE_RETEST = test_item_data.onlineRetest;
        //    MAX_OFFLINE_RETEST = 0,
        sub_test_item_detail.MAX_OFFLINE_RETEST = Some(0);
        //    MAX_ONLINE_RETEST = 0,
        sub_test_item_detail.MAX_ONLINE_RETEST = Some(0);
        //    IS_DIE_FIRST_TEST = 0,
        sub_test_item_detail.IS_DIE_FIRST_TEST = Some(0);
        //    IS_DIE_FINAL_TEST = 0,
        sub_test_item_detail.IS_DIE_FINAL_TEST = Some(0);
        //    IS_FIRST_TEST = 0,
        sub_test_item_detail.IS_FIRST_TEST = Some(0);
        //    IS_FINAL_TEST = 0,
        sub_test_item_detail.IS_FINAL_TEST = Some(0);
        //    IS_FIRST_TEST_IGNORE_TP = 1,
        sub_test_item_detail.IS_FIRST_TEST_IGNORE_TP = Some(1);
        //    IS_FINAL_TEST_IGNORE_TP = 1,
        sub_test_item_detail.IS_FINAL_TEST_IGNORE_TP = Some(1);
        //    IS_DUP_FIRST_TEST = 0,
        sub_test_item_detail.IS_DUP_FIRST_TEST = Some(0);
        //    IS_DUP_FINAL_TEST = 0,
        sub_test_item_detail.IS_DUP_FINAL_TEST = Some(0);
        //    IS_DUP_FIRST_TEST_IGNORE_TP = 0,
        sub_test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP = Some(0);
        //    IS_DUP_FINAL_TEST_IGNORE_TP = 0,
        sub_test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP = Some(0);
        //    TEST_SUITE = EMPTY,
        sub_test_item_detail.TEST_SUITE = Some(String::new());
        //    CONDITION_SET = if (testItemData.conditionSet != null) testItemData.conditionSet else Map(),
        if let Some(condition_set) = test_item_data.conditionSet.clone() {
            sub_test_item_detail.CONDITION_SET = Some(condition_set)
        };
        //    TEST_NUM = testItemData.testNum,
        sub_test_item_detail.TEST_NUM = test_item_data.testNum;
        //    TEST_TXT = testItemData.testTxt,
        sub_test_item_detail.TEST_TXT = test_item_data.testTxt.clone();
        //    TEST_ITEM = testItemData.testItem,
        sub_test_item_detail.TEST_ITEM = test_item_data.testItem.clone();
        //    IS_DIE_FIRST_TEST_ITEM = 0,
        sub_test_item_detail.IS_DIE_FIRST_TEST_ITEM = Some(0);
        //    TESTITEM_TYPE = testItemData.testitemType,
        sub_test_item_detail.TESTITEM_TYPE = test_item_data.testitemType.clone();
        //    TEST_FLG = testItemData.testFlg,
        sub_test_item_detail.TEST_FLG = test_item_data.testFlg.clone();
        //    PARM_FLG = testItemData.parmFlg,
        sub_test_item_detail.PARM_FLG = test_item_data.parmFlg.clone();
        //    TEST_STATE = calculateTestState(testItemData, fileCategory),
        sub_test_item_detail.TEST_STATE =
            Some(DwdTableService::calculate_test_state_file_category(test_item_data, file_category));
        //    TEST_VALUE = calculateTestValue(testItemData, fileCategory, needMultiplyScale),
        sub_test_item_detail.TEST_VALUE =
            DwdTableService::calculate_test_value(test_item_data, file_category, need_multiply_scale);
        //    UNITS = standardUnits.getOrElse(stringValue(calculateResScal(testItemData, fileCategory)), EMPTY) + stringValue(testItemData.units),
        let res_scal_str = res_scal.map(|res_scal| res_scal.to_string()).unwrap_or(String::new());
        sub_test_item_detail.UNITS = Some(format!(
            "{}{}",
            standard_units
                .get(&res_scal_str)
                .map(|units| units.to_string())
                .unwrap_or(String::new()),
            test_item_data.units.as_deref().unwrap_or("")
        ));
        //    TEST_RESULT = calculateTestResult(testItemData, fileCategory),
        sub_test_item_detail.TEST_RESULT =
            DwdTableService::calculate_test_result_file_category(test_item_data, file_category);
        //    ORIGIN_TEST_VALUE = null,
        sub_test_item_detail.ORIGIN_TEST_VALUE = None;
        //    ORIGIN_UNITS = testItemData.units,
        sub_test_item_detail.ORIGIN_UNITS = test_item_data.units.clone();
        //    TEST_ORDER = testItemData.testOrder,
        sub_test_item_detail.TEST_ORDER = test_item_data.testOrder;
        //    ALARM_ID = testItemData.alarmId,
        sub_test_item_detail.ALARM_ID = test_item_data.alarmId.clone();
        //    OPT_FLG = testItemData.optFlg,
        sub_test_item_detail.OPT_FLG = test_item_data.optFlg.clone();
        //    RES_SCAL = calculateResScal(testItemData, fileCategory),
        sub_test_item_detail.RES_SCAL = res_scal;
        //    NUM_TEST = testItemData.numTest,
        sub_test_item_detail.NUM_TEST = test_item_data.numTest;
        //    LLM_SCAL = calculateLlmScal(testItemData, fileCategory),
        sub_test_item_detail.LLM_SCAL = DwdTableService::calculate_llm_scal(test_item_data, file_category);
        //    HLM_SCAL = calculateHlmScal(testItemData, fileCategory),
        sub_test_item_detail.HLM_SCAL = DwdTableService::calculate_hlm_scal(test_item_data, file_category);
        //    LO_LIMIT = calculateLoLimit(testItemData, fileCategory, needMultiplyScale),
        sub_test_item_detail.LO_LIMIT =
            DwdTableService::calculate_lo_limit(test_item_data, file_category, need_multiply_scale);
        //    HI_LIMIT = calculateHiLimit(testItemData, fileCategory, needMultiplyScale),
        sub_test_item_detail.HI_LIMIT =
            DwdTableService::calculate_hi_limit(test_item_data, file_category, need_multiply_scale);
        //    ORIGIN_HI_LIMIT = toBigDecimal(testItemData.hiLimit),
        sub_test_item_detail.ORIGIN_HI_LIMIT = test_item_data.hiLimit;
        //    ORIGIN_LO_LIMIT = toBigDecimal(testItemData.loLimit),
        sub_test_item_detail.ORIGIN_LO_LIMIT = test_item_data.loLimit;
        //    C_RESFMT = testItemData.cResfmt,
        sub_test_item_detail.C_RESFMT = test_item_data.cResfmt.clone();
        //    C_LLMFMT = testItemData.cLlmfmt,
        sub_test_item_detail.C_LLMFMT = test_item_data.cLlmfmt.clone();
        //    C_HLMFMT = testItemData.cHlmfmt,
        sub_test_item_detail.C_HLMFMT = test_item_data.cHlmfmt.clone();
        //    LO_SPEC = toBigDecimal(testItemData.loSpec),
        sub_test_item_detail.LO_SPEC = test_item_data.loSpec;
        //    HI_SPEC = toBigDecimal(testItemData.hiSpec),
        sub_test_item_detail.HI_SPEC = test_item_data.hiSpec;
        //    HBIN_NUM = testItemData.hbinNum,
        sub_test_item_detail.HBIN_NUM = test_item_data.hbinNum;
        //    SBIN_NUM = testItemData.sbinNum,
        sub_test_item_detail.SBIN_NUM = test_item_data.sbinNum;
        //    SBIN_PF = sbinPf,
        sub_test_item_detail.SBIN_PF = Some(sbin_pf);
        //    SBIN_NAM = testItemData.sbinNam,
        sub_test_item_detail.SBIN_NAM = test_item_data.sbinNam.clone();
        //    HBIN_PF = hbinPf,
        sub_test_item_detail.HBIN_PF = Some(hbin_pf);
        //    HBIN_NAM = testItemData.hbinNam,
        sub_test_item_detail.HBIN_NAM = test_item_data.hbinNam.clone();
        //    HBIN = buildBin(HBIN_NAME_PREFIX, testItemData.hbinNum, testItemData.hbinNam),
        sub_test_item_detail.HBIN = Some(DwdTableService::build_bin(
            HBIN_NAME_PREFIX,
            test_item_data.hbinNum,
            test_item_data.hbinNam.as_deref(),
        ));
        //    SBIN = buildBin(SBIN_NAME_PREFIX, testItemData.sbinNum, testItemData.sbinNam),
        sub_test_item_detail.SBIN = Some(DwdTableService::build_bin(
            SBIN_NAME_PREFIX,
            test_item_data.sbinNum,
            test_item_data.sbinNam.as_deref(),
        ));
        //    TEST_HEAD = testItemData.testHead,
        sub_test_item_detail.TEST_HEAD = test_item_data.testHead;
        //    PART_FLG = testItemData.partFlg,
        sub_test_item_detail.PART_FLG = test_item_data.partFlg.clone();
        //    PART_ID = testItemData.partId,
        sub_test_item_detail.PART_ID = test_item_data.partId.clone();
        //    C_PART_ID = testItemData.cPartId.longValue(),
        sub_test_item_detail.C_PART_ID = test_item_data.cPartId.map(|c_part_id| c_part_id as i64);
        //    ECID = if (StringUtils.isNotEmpty(testItemData.ecid) && (testItemData.ecid.split(Constant.UNDER_LINE).length >= 4)) testItemData.ecid else EMPTY,
        if let Some(ecid) = test_item_data.ecid.clone() {
            if ecid.split(UNDER_LINE).count() >= 4 {
                sub_test_item_detail.ECID = Some(ecid);
            } else {
                sub_test_item_detail.ECID = Some(String::new());
            }
        } else {
            sub_test_item_detail.ECID = Some(String::new());
        };
        //    ECID_EXT = stringValue(testItemData.ecidExt),
        sub_test_item_detail.ECID_EXT = test_item_data.ecidExt.clone();
        //    ECID_EXTRA = if (testItemData.ecidExtra != null) testItemData.ecidExtra else Map(),
        if let Some(ecid_extra) = test_item_data.ecidExtra.clone() {
            sub_test_item_detail.ECID_EXTRA = Some(ecid_extra);
        }
        //    IS_STANDARD_ECID = 1,
        sub_test_item_detail.IS_STANDARD_ECID = Some(1);
        //    X_COORD = testItemData.xCoord,
        sub_test_item_detail.X_COORD = test_item_data.xCoord;
        //    Y_COORD = testItemData.yCoord,
        sub_test_item_detail.Y_COORD = test_item_data.yCoord;
        //    DIE_X = testItemData.xCoord,
        sub_test_item_detail.DIE_X = test_item_data.xCoord;
        //    DIE_Y = testItemData.yCoord,
        sub_test_item_detail.DIE_Y = test_item_data.yCoord;
        //    TEST_TIME = testItemData.testTime,
        sub_test_item_detail.TEST_TIME = test_item_data.testTime;
        //    PART_TXT = testItemData.partTxt,
        sub_test_item_detail.PART_TXT = test_item_data.partTxt.clone();
        //    PART_FIX = testItemData.partFix,
        sub_test_item_detail.PART_FIX = test_item_data.partFix.clone();
        //    SITE = testItemData.site,
        sub_test_item_detail.SITE = test_item_data.site.clone();
        //    TOUCH_DOWN_ID = testItemData.touchDownId,
        sub_test_item_detail.TOUCH_DOWN_ID = test_item_data.touchDownId.clone();
        //    WAFER_LOT_ID = EMPTY,
        sub_test_item_detail.WAFER_LOT_ID = Some(String::new());
        //    WAFER_ID = EMPTY,
        sub_test_item_detail.WAFER_ID = Some(String::new());
        //    WAFER_NO = EMPTY,
        sub_test_item_detail.WAFER_NO = Some(String::new());
        //    RETICLE_T_X = null,
        sub_test_item_detail.RETICLE_T_X = None;
        //    RETICLE_T_Y = null,
        sub_test_item_detail.RETICLE_T_Y = None;
        //    RETICLE_X = null,
        sub_test_item_detail.RETICLE_X = None;
        //    RETICLE_Y = null,
        sub_test_item_detail.RETICLE_Y = None;
        //    SITE_ID = EMPTY,
        sub_test_item_detail.SITE_ID = Some(String::new());
        //    VECT_NAM = testItemData.vectNam,
        sub_test_item_detail.VECT_NAM = test_item_data.vectNam.clone();
        //    TIME_SET = testItemData.timeSet,
        sub_test_item_detail.TIME_SET = test_item_data.timeSet.clone();
        //    NUM_FAIL = testItemData.numFail,
        sub_test_item_detail.NUM_FAIL = test_item_data.numFail;
        //    FAIL_PIN = testItemData.failPin,
        sub_test_item_detail.FAIL_PIN = test_item_data.failPin.clone();
        //    CYCL_CNT = testItemData.cyclCnt,
        sub_test_item_detail.CYCL_CNT = test_item_data.cyclCnt;
        //    REPT_CNT = testItemData.reptCnt,
        sub_test_item_detail.REPT_CNT = test_item_data.reptCnt;
        //    LONG_ATTRIBUTE_SET = Map(),
        sub_test_item_detail.LONG_ATTRIBUTE_SET = None;
        //    STRING_ATTRIBUTE_SET = Map(),
        sub_test_item_detail.STRING_ATTRIBUTE_SET = None;
        //    FLOAT_ATTRIBUTE_SET = Map(),
        sub_test_item_detail.FLOAT_ATTRIBUTE_SET = None;
        //    UID = EMPTY,
        sub_test_item_detail.UID = Some(String::new());
        //    TEXT_DAT = stringValue(testItemData.textDat),
        sub_test_item_detail.TEXT_DAT = test_item_data.textDat.clone();
        //    CREATE_HOUR_KEY = createHourKey,
        sub_test_item_detail.CREATE_HOUR_KEY = Some(create_hour_key);
        //    CREATE_DAY_KEY = createDayKey,
        sub_test_item_detail.CREATE_DAY_KEY = Some(create_day_key);
        //    CREATE_TIME = now,
        sub_test_item_detail.CREATE_TIME = now.timestamp_millis();
        //    EFUSE_EXTRA = if (testItemData.efuseExtra != null) testItemData.efuseExtra else Map(),
        if let Some(efuse_extra) = test_item_data.efuseExtra.clone() {
            sub_test_item_detail.EFUSE_EXTRA = Some(efuse_extra);
        }
        //    CHIP_ID = stringValue(testItemData.chipId))
        sub_test_item_detail.CHIP_ID = test_item_data.chipId.clone();
        Ok(sub_test_item_detail)
    }

    /// Merge SubTestItemDetail with FileDetail to create complete TestItemDetail
    ///
    /// Corresponds to: TestItemDetailCommonService.scala:148-193
    /// def getTestItemDetailWithFileInfo(subTestItemDetail: SubTestItemDetail, fileDetail: FileDetail): TestItemDetail
    pub fn get_test_item_detail_with_file_info<'a>(
        sub_test_item_detail: &'static SubTestItemDetail,
        file_detail: &'a FileDetail,
    ) -> Result<TestItemDetailRow<'a>, Box<dyn Error>> {
        Ok(TestItemDetailRow::new(sub_test_item_detail, file_detail))
    }

    /// Fill DieTestInfo into SubTestItemDetail (CRITICAL for ECID processing)
    ///
    /// Corresponds to: TestItemDetailCommonService.scala:203-232
    /// def fillDieTestInfo(subTestItemDetail: SubTestItemDetail, dieTestInfoMap: Map[DieKey, DieTestInfo]): SubTestItemDetail
    pub fn fill_die_test_info(
        &self,
        mut sub_test_item_detail: SubTestItemDetail,
        die_test_info_map: &HashMap<DieKey, DieTestInfo>,
        test_num_force_zero_test_program_list: &[String],
    ) -> Result<SubTestItemDetail, Box<dyn Error>> {
        // Scala代码
        // val dieKey = DieKey(subTestItemDetail.FILE_ID, subTestItemDetail.C_PART_ID)
        let die_key = DieKey::new(sub_test_item_detail.FILE_ID.unwrap(), sub_test_item_detail.C_PART_ID.unwrap());
        // val dieTestInfo = dieTestInfoMap.getOrElse(dieKey, null)
        let die_test_info = die_test_info_map.get(&die_key);
        // var testNumForceZeroFlag = false
        let mut test_num_force_zero_flag = false;
        // if (dieTestInfo != null) {
        if let Some(die_test_info) = die_test_info {
            //   subTestItemDetail.WAFER_LOT_ID = dieTestInfo.WAFER_LOT_ID
            sub_test_item_detail.WAFER_LOT_ID = die_test_info.wafer_lot_id.clone();
            //   subTestItemDetail.WAFER_ID = dieTestInfo.WAFER_ID
            sub_test_item_detail.WAFER_ID = die_test_info.wafer_id.clone();
            //   subTestItemDetail.WAFER_NO = dieTestInfo.WAFER_NO
            sub_test_item_detail.WAFER_NO = die_test_info.wafer_no.clone();
            //   subTestItemDetail.IS_DIE_FIRST_TEST = dieTestInfo.IS_FIRST_TEST
            sub_test_item_detail.IS_DIE_FIRST_TEST = die_test_info.is_first_test;
            //   subTestItemDetail.IS_DIE_FINAL_TEST = dieTestInfo.IS_FINAL_TEST
            sub_test_item_detail.IS_DIE_FINAL_TEST = die_test_info.is_final_test;
            //   subTestItemDetail.IS_FIRST_TEST = dieTestInfo.IS_FIRST_TEST
            sub_test_item_detail.IS_FIRST_TEST = die_test_info.is_first_test;
            //   subTestItemDetail.IS_FINAL_TEST = dieTestInfo.IS_FINAL_TEST
            sub_test_item_detail.IS_FINAL_TEST = die_test_info.is_final_test;
            //   subTestItemDetail.IS_FIRST_TEST_IGNORE_TP = dieTestInfo.IS_FIRST_TEST_IGNORE_TP
            sub_test_item_detail.IS_FIRST_TEST_IGNORE_TP = die_test_info.is_first_test_ignore_tp;
            //   subTestItemDetail.IS_FINAL_TEST_IGNORE_TP = dieTestInfo.IS_FINAL_TEST_IGNORE_TP
            sub_test_item_detail.IS_FINAL_TEST_IGNORE_TP = die_test_info.is_final_test_ignore_tp;
            //   subTestItemDetail.MAX_OFFLINE_RETEST = dieTestInfo.MAX_OFFLINE_RETEST
            sub_test_item_detail.MAX_OFFLINE_RETEST = die_test_info.max_offline_retest;
            //   subTestItemDetail.MAX_ONLINE_RETEST = dieTestInfo.MAX_ONLINE_RETEST
            sub_test_item_detail.MAX_ONLINE_RETEST = die_test_info.max_online_retest;
            //   subTestItemDetail.IS_DUP_FIRST_TEST = dieTestInfo.IS_DUP_FIRST_TEST
            sub_test_item_detail.IS_DUP_FIRST_TEST = die_test_info.is_dup_first_test;
            //   subTestItemDetail.IS_DUP_FINAL_TEST = dieTestInfo.IS_DUP_FINAL_TEST
            sub_test_item_detail.IS_DUP_FINAL_TEST = die_test_info.is_dup_final_test;
            //   subTestItemDetail.IS_DUP_FIRST_TEST_IGNORE_TP = dieTestInfo.IS_DUP_FIRST_TEST_IGNORE_TP
            sub_test_item_detail.IS_DUP_FIRST_TEST_IGNORE_TP = die_test_info.is_dup_first_test_ignore_tp;
            //   subTestItemDetail.IS_DUP_FINAL_TEST_IGNORE_TP = dieTestInfo.IS_DUP_FINAL_TEST_IGNORE_TP
            sub_test_item_detail.IS_DUP_FINAL_TEST_IGNORE_TP = die_test_info.is_dup_final_test_ignore_tp;
            //   subTestItemDetail.X_COORD = dieTestInfo.X_COORD
            sub_test_item_detail.X_COORD = die_test_info.x_coord;
            //   subTestItemDetail.Y_COORD = dieTestInfo.Y_COORD
            sub_test_item_detail.Y_COORD = die_test_info.y_coord;
            //   subTestItemDetail.ECID = dieTestInfo.ECID
            sub_test_item_detail.ECID = die_test_info.ecid.clone();
            //   subTestItemDetail.IS_STANDARD_ECID = dieTestInfo.IS_STANDARD_ECID
            sub_test_item_detail.IS_STANDARD_ECID = die_test_info.is_standard_ecid;
            //   subTestItemDetail.UID = dieTestInfo.UID
            sub_test_item_detail.UID = die_test_info.uid.clone();
            //   subTestItemDetail.CHIP_ID = dieTestInfo.CHIP_ID
            sub_test_item_detail.CHIP_ID = die_test_info.chip_id.clone();
            //   subTestItemDetail.ECID_EXTRA = dieTestInfo.ECID_EXTRA
            if let Some(ecid_extra) = die_test_info.ecid_extra.clone() {
                sub_test_item_detail.ECID_EXTRA = Some(ecid_extra);
            }
            //   subTestItemDetail.EFUSE_EXTRA = dieTestInfo.EFUSE_EXTRA
            if let Some(efuse_extra) = die_test_info.efuse_extra.clone() {
                sub_test_item_detail.EFUSE_EXTRA = Some(efuse_extra);
            }
            // testNumForceZeroFlag = testNumForceZeroTestProgramList.contains(dieTestInfo.TEST_PROGRAM)
            if let Some(test_program) = die_test_info.test_program.clone() {
                test_num_force_zero_flag = test_num_force_zero_test_program_list.contains(&test_program);
            }
        }
        // clearTestNum(subTestItemDetail, testNumForceZeroFlag)
        self.clear_test_num(sub_test_item_detail, test_num_force_zero_flag)
    }

    /// Clear test number in SubTestItemDetail (set testNum to 0)
    ///
    /// Corresponds to: TestItemDetailCommonService.scala:242-253
    /// def clearTestNum(subTestItemDetail: SubTestItemDetail, testNumForceZeroFlag: Boolean): SubTestItemDetail
    pub fn clear_test_num(
        &self,
        mut sub_test_item_detail: SubTestItemDetail,
        test_num_force_zero_flag: bool,
    ) -> Result<SubTestItemDetail, Box<dyn Error>> {
        // 将测试编号设置为0，并重建testItem
        // Corresponds to Scala clearTestNum lines 244-246
        let test_txt: &str = sub_test_item_detail.TEST_TXT.as_deref().unwrap();
        if test_num_force_zero_flag {
            sub_test_item_detail.TEST_NUM = Some(0);
            sub_test_item_detail.TEST_ITEM =
                Some(DwdTableService::build_test_item(sub_test_item_detail.TEST_NUM, test_txt));
        } else if sub_test_item_detail.TEST_ITEM.as_deref().unwrap_or("").trim().is_empty() {
            // HDFS历史数据中缺少testItem字段
            // Corresponds to Scala clearTestNum lines 247-250
            sub_test_item_detail.TEST_ITEM =
                Some(DwdTableService::build_test_item(sub_test_item_detail.TEST_NUM, test_txt));
        }

        Ok(sub_test_item_detail)
    }
}

impl Default for TestItemDetailCommonService {
    fn default() -> Self {
        Self::new()
    }
}
