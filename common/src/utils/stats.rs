use average::Estimate;
use average::{Mean, Variance};

// 无偏偏度计算
pub fn calculate_skewness(data: &[f64]) -> Option<f64> {
    let n = data.len() as f64;
    if n < 3.0 {
        return None; // 样本不足无法计算无偏偏度
    }

    let mean = data.iter().sum::<f64>() / n;

    let (sum2, sum3) = data.iter().fold((0.0, 0.0), |(s2, s3), &x| {
        let dev = x - mean;
        (s2 + dev * dev, s3 + dev * dev * dev)
    });

    // 计算中心矩
    let m2 = sum2 / n;
    let m3 = sum3 / n;

    if m2 <= 1e-12 {
        // 处理极小方差
        return None;
    }

    // 原始偏度计算
    let g1 = m3 / m2.powf(1.5);

    // 无偏修正系数
    let correction = (n * (n - 1.0)).sqrt() / (n - 2.0);

    Some(g1 * correction)
}

// 无偏峰度计算
pub fn calculate_kurtosis(data: &[f64]) -> Option<f64> {
    let n = data.len() as f64;
    if n < 4.0 {
        return None; // 样本不足无法计算无偏峰度
    }

    let mean = data.iter().sum::<f64>() / n;

    let (sum2, sum4) = data.iter().fold((0.0, 0.0), |(s2, s4), &x| {
        let dev = x - mean;
        let dev2 = dev * dev;
        (s2 + dev2, s4 + dev2 * dev2)
    });

    // 无偏方差计算
    let variance = sum2 / (n - 1.0);

    if variance == 0.0 {
        return None; // 方差为0无法计算峰度
    }

    // 无偏峰度计算
    let term1 = (n * (n + 1.0)) / ((n - 1.0) * (n - 2.0) * (n - 3.0));
    let term2 = sum4 / (variance.powi(2));
    let term3 = (3.0 * (n - 1.0).powi(2)) / ((n - 2.0) * (n - 3.0));

    Some(term1 * term2 - term3)
}

pub fn calculate_square_sum(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    Some(data.iter().map(|x| x * x).sum())
}

pub fn calculate_sum(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    Some(data.iter().sum())
}

pub fn calculate_mean(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut mean = Mean::new();
    data.iter().for_each(|&x| mean.add(x));
    Some(mean.mean())
}

pub fn calculate_std_dev(data: &[f64]) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut variance = Variance::new();
    data.iter().for_each(|&x| variance.add(x));
    Some(variance.estimate().sqrt())
}

pub fn calculate_quantile(data: &[f64], p: f64) -> Option<f64> {
    if data.is_empty() {
        return None;
    }

    let mut sorted_data = data.to_vec();
    sorted_data.sort_by(|a, b| a.partial_cmp(b).unwrap());

    let n = data.len();
    let px = p / 100.0 * (n - 1) as f64;
    let i = px.floor() as usize;
    let g = px - i as f64;

    if g == 0.0 {
        Some(sorted_data[i])
    } else {
        Some((1.0 - g) * sorted_data[i] + g * sorted_data[i + 1])
    }
}

pub fn calculate_cp(std_dev: f64, low_limit: f64, high_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        f64::MAX
    } else {
        divide(high_limit - low_limit, 6.0 * std_dev)
    }
}

pub fn calculate_cpk(mean: f64, std_dev: f64, low_limit: f64, high_limit: f64) -> f64 {
    calculate_cpu(mean, std_dev, high_limit).min(calculate_cpl(mean, std_dev, low_limit))
}

pub fn calculate_cpu(mean: f64, std_dev: f64, high_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        f64::MAX
    } else {
        divide(high_limit - mean, 3.0 * std_dev)
    }
}

pub fn calculate_cpl(mean: f64, std_dev: f64, low_limit: f64) -> f64 {
    if equals(std_dev, 0.0) {
        f64::MAX
    } else {
        divide(mean - low_limit, 3.0 * std_dev)
    }
}

// 辅助函数，用于保持与Scala版本一致的精度
fn equals(a: f64, b: f64) -> bool {
    (a - b).abs() < std::f64::EPSILON
}

fn divide(a: f64, b: f64) -> f64 {
    // 这里要跟scala版本的精度保持一致
    divide_with_scale(a, b, 6)
}

fn divide_with_scale(a: f64, b: f64, scale: i32) -> f64 {
    if equals(b, 0.0) {
        0.0
    } else {
        let scale_factor = 10.0_f64.powi(scale);
        (a / b * scale_factor).round() / scale_factor
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_cp_calculation() {
        let values = vec![1.0, 1.1, 1.2, 1.3, 1.4, 2.0, 2.1, 5.0, 5.5, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0];
        let low_limit = 1.0;
        let high_limit = 10.0;

        // 计算标准差
        let std_dev = calculate_std_dev(&values).unwrap();
        println!("Standard Deviation: {}", std_dev);

        // 计算 Cp
        let cp = calculate_cp(std_dev, low_limit, high_limit);
        println!("Cp: {}", cp);

        // 打印中间计算步骤
        let range = high_limit - low_limit;
        println!("Range (high - low): {}", range);
        let denominator = 6.0 * std_dev;
        println!("Denominator (6 * std_dev): {}", denominator);
    }

    #[test]
    fn test_cpk_calculation() {
        let values = vec![1.0, 1.1, 1.2, 1.3, 1.4, 2.0, 2.1, 5.0, 5.5, 6.0, 7.0, 8.0, 10.0, 12.0, 15.0];
        let low_limit = 1.0;
        let high_limit = 10.0;

        let std_dev = calculate_std_dev(&values).unwrap();
        let mean = calculate_mean(&values).unwrap();

        println!("Mean: {}", mean);
        println!("Standard Deviation: {}", std_dev);

        let cpu = calculate_cpu(mean, std_dev, high_limit);
        let cpl = calculate_cpl(mean, std_dev, low_limit);
        let cpk = calculate_cpk(mean, std_dev, low_limit, high_limit);

        println!("CPU: {}", cpu);
        println!("CPL: {}", cpl);
        println!("Cpk: {}", cpk);

        // 打印中间计算步骤
        println!("CPU calculation:");
        println!("  (high_limit - mean): {}", high_limit - mean);
        println!("  (3.0 * std_dev): {}", 3.0 * std_dev);

        println!("CPL calculation:");
        println!("  (mean - low_limit): {}", mean - low_limit);
        println!("  (3.0 * std_dev): {}", 3.0 * std_dev);
    }

    #[test]
    fn test_divide_with_scale() {
        assert_eq!(divide_with_scale(10.0, 3.0, 1), 3.3);
        assert_eq!(divide_with_scale(10.0, 3.0, 2), 3.33);
        assert_eq!(divide_with_scale(10.0, 3.0, 3), 3.333);
        assert_eq!(divide_with_scale(10.0, 0.0, 2), 0.0);
    }
}
