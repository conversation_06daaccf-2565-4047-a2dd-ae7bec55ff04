use crate::config::DwTestItemConfig;
use crate::error::DatawareError::DwdCalculateFailed;
use bumpalo::Bump;
use ck_provider::{AsyncCkChannel, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig, StreamMetrics};
use common::dto::dwd::die_detail_parquet::DieDetailParquet;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::dwd::test_item_detail_row::TestItemDetailRow;
use common::dto::ods::test_item_data_parquet::TestItemDataParquet;
use common::dwd::dwd_service::DwdService;
use common::dwd::model::value::die_test_info::DieTestInfo;
use common::dwd::sink::test_item_detail_handler::TestItemDetailHandler;
use common::dwd::table::distributed::test_item_detail_service::TestItemDetailService;
use common::dwd::table::test_item_detail_common_service::TestItemDetailCommonService;
use common::model::constant::upload_type::UploadType;
use common::model::key::{die_key::DieKey, wafer_key::WaferKey};
use common::repository::mysql::test_num_force_zero_config_repository::TestNumForceZeroConfigRepository;
use common::utils::path;
use mysql_provider::MySqlConfig;
use parquet_provider::hdfs_provider::HdfsConfig;
use parquet_provider::parquet_provider::write_parquet_multi;
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::Semaphore;
// Corresponding to Scala file:
// /dataware/dataware-dw/dataware-dw-test-item/src/main/scala/com/guwave/onedata/dataware/dw/testItem/spark/dwd/service/impl/CpDwdTestItemService.scala

/// CpDwdTestItemService handles CP (Contact Probe) stage DWD layer test item processing
/// This service orchestrates the calculation of test item details for the CP test stage
#[derive(Debug, Clone)]
pub struct CpDwdTestItemService {
    test_item_detail_result_partition: i32,
    test_item_detail_service: TestItemDetailService,
    mysql_config: MySqlConfig,
    properties: DwTestItemConfig,
}

/// Calculation result containing broadcast maps and datasets
/// Corresponds to the return type of calculate method in Scala (line 30)
#[derive(Debug)]
pub struct CpDwdCalculationResult {
    pub file_detail_map: HashMap<u64, FileDetail>,
    pub die_detail: Vec<DieDetailParquet>,
    pub test_item_detail: Vec<Vec<SubTestItemDetail>>,
}

impl CpDwdTestItemService {
    /// Create new CpDwdTestItemService
    ///
    /// Corresponds to: CpDwdTestItemService.scala:27 (case class constructor)
    /// case class CpDwdTestItemService(properties: DwTestItemProperties, testItemDetailResultPartition: Int)
    pub fn new(test_item_detail_result_partition: i32, test_area: String) -> Self {
        let config: DwTestItemConfig = DwTestItemConfig::get_config().unwrap();
        Self {
            test_item_detail_result_partition,
            test_item_detail_service: TestItemDetailService::new(test_area),
            mysql_config: config.get_mysql_config(),
            properties: config,
        }
    }

    /// Calculate CP DWD test item details
    ///
    /// Corresponds to: CpDwdTestItemService.scala:30-90
    /// def calculate(spark: SparkSession, dieDetailSource: Dataset[DieDetail], testItemData: Dataset[TestItemData],
    ///              waferKey: WaferKey, testArea: String, executeMode: String, fileCategory: String,
    ///              ckSinkType: String, runMode: String): (Broadcast[Map[lang.Long, FileDetail]], Dataset[DieDetail], Dataset[SubTestItemDetail])
    pub async fn calculate(
        &self,
        die_detail_source: Vec<DieDetailParquet>,
        test_item_data: Vec<Vec<TestItemDataParquet>>,
        wafer_key: &WaferKey,
        test_area: String,
        _execute_mode: String,
        file_category: String,
        _ck_sink_type: String,
        _run_mode: String,
        config: &DwTestItemConfig,
    ) -> Result<CpDwdCalculationResult, Box<dyn Error + Send + Sync>> {
        log::info!("当前正在计算的waferNo: {}", wafer_key);
        let die_detail = die_detail_source;
        let test_num_force_zero_test_program_list = TestNumForceZeroConfigRepository::new(self.mysql_config.clone())
            .await?
            .read_test_num_force_zero_test_program_list(
                wafer_key.customer.clone(),
                wafer_key.sub_customer.clone(),
                wafer_key.factory.clone(),
                wafer_key.factory_site.clone(),
                test_area.to_string(),
                wafer_key.device_id.clone(),
                wafer_key.test_stage.clone(),
                die_detail
                    .iter()
                    .map(|die| die.TEST_PROGRAM.clone().unwrap())
                    .collect::<Vec<String>>(),
                UploadType::AUTO.to_string(),
            )
            .await?;

        let die_test_info_map = Arc::new(self.build_die_test_info_broadcast_map(&die_detail)?);
        let file_detail_map = self.build_file_detail_broadcast_map(&die_detail)?;

        // 使用rayon并行处理每个Vec<TestItemDataParquet>
        let service = Arc::new(self.test_item_detail_service.clone());
        let file_category = Arc::new(file_category.clone());
        let need_multiply_scale = self.properties.get_need_multiply_scale(wafer_key.customer.as_str());
        let standard_units = Arc::new(self.properties.standard_units.clone());
        let need_clear_invalid_data = self.properties.get_clear_invalid_data_flag(wafer_key.customer.as_str());
        let test_num_force_zero_list = Arc::new(test_num_force_zero_test_program_list.clone());

        let test_item_detail: Result<Vec<Vec<SubTestItemDetail>>, Box<dyn Error + Send + Sync>> = test_item_data
            .into_par_iter()
            .enumerate()
            .map(|(index, data_batch)| {
                log::info!("开始处理第{}批数据，数据量: {}", index, data_batch.len());
                let result = service
                    .calculate_cp_test_item_detail(
                        data_batch,
                        &file_category,
                        &die_test_info_map,
                        need_multiply_scale,
                        &standard_units,
                        need_clear_invalid_data,
                        &test_num_force_zero_list,
                    )
                    .map_err(|e| -> Box<dyn Error + Send + Sync> {
                        Box::new(std::io::Error::new(
                            std::io::ErrorKind::Other,
                            format!("批次{}处理失败: {}", index, e),
                        ))
                    });
                log::info!("完成处理第{}批数据", index);
                result
            })
            .collect();

        let test_item_detail = test_item_detail?;

        log::info!("test_item_detail length: {:?}", test_item_detail.len());

        let test_item_detail_path = path::get_dwd_wafer_path(
            &self.properties.cp_test_item_detail_result_dir,
            &test_area,
            &wafer_key.customer,
            &wafer_key.factory,
            &wafer_key.lot_id,
            &wafer_key.wafer_no,
            &wafer_key.device_id,
            &wafer_key.test_stage,
            &wafer_key.lot_type,
        );

        log::info!("写入parquet文件到路径: {}", test_item_detail_path);

        let test_item_detail_refs = test_item_detail.iter().map(|a| a).collect();
        write_parquet_multi(
            &test_item_detail_path,
            &test_item_detail_refs,
            Some(&HdfsConfig::default()),
            config.get_batch_size()?,
        )
        .await?;
        log::info!("成功写入parquet文件到路径: {}", test_item_detail_path);

        // 并发写入每个批次到ClickHouse
        let (test_item_detail, file_detail_map) = self
            .write_test_item_detail_to_clickhouse_concurrent(test_item_detail, file_detail_map, config)
            .await?;

        Ok(CpDwdCalculationResult { file_detail_map, die_detail, test_item_detail })
    }

    async fn write_test_item_detail_to_clickhouse_concurrent(
        &self,
        test_item_detail: Vec<Vec<SubTestItemDetail>>,
        file_detail_map: HashMap<u64, FileDetail>,
        config: &DwTestItemConfig,
    ) -> Result<(Vec<Vec<SubTestItemDetail>>, HashMap<u64, FileDetail>), Box<dyn Error + Send + Sync>> {
        let ck_config = self.properties.get_ck_config(self.properties.dwd_db_name.as_str());
        let test_item_detail_handler = Arc::new(TestItemDetailHandler::new(
            self.properties.dwd_db_name.clone(),
            self.properties.insert_cluster_table,
        ));

        let db_table_name = format!("{}.{}", test_item_detail_handler.db_name, test_item_detail_handler.table_name);
        let batch_size = config.get_batch_size()?;

        // 配置流式处理参数
        let stream_config = StreamConfig::default()
            .with_buffer_size(batch_size * 2)
            .with_batch_size(batch_size)
            .with_flush_interval(Duration::from_secs(2))
            .with_max_retries(3)
            .with_backpressure_timeout(Duration::from_secs(600))
            .with_parallel_flush(true)
            .with_max_concurrent_flushes(test_item_detail.len() * 2);

        // 创建指标收集器
        let metrics = Arc::new(StreamMetrics::new());

        // 创建流式通道
        let (sender, receiver) = AsyncCkChannel::new::<TestItemDetailRow>(stream_config.clone(), metrics.clone());

        let ck_provider = CkProviderImpl::new(ck_config.clone());

        // 创建流处理器
        let mut processor = CkStreamProcessorBuilder::new()
            .with_receiver(receiver)
            .with_provider(ck_provider.clone())
            .with_metrics(metrics.clone())
            .with_config(stream_config)
            .with_table_name(db_table_name)
            .build()?;

        // 启动流处理器任务
        let processor_handle = tokio::spawn(async move {
            if let Err(e) = processor.start().await {
                eprintln!("流处理器错误: {:?}", e);
            }
        });

        // 直接在这里执行并发写入，避免数据传递和克隆
        let file_detail_map_arc = Arc::new(file_detail_map);
        let test_item_detail_arc = Arc::new(test_item_detail);

        // 限制并发数，避免创建过多任务导致系统过载
        let max_concurrent_batches = std::cmp::min(test_item_detail_arc.len(), 8);
        let semaphore = Arc::new(Semaphore::new(max_concurrent_batches));

        // 创建并发任务处理每个批次
        let mut tasks = Vec::new();
        for batch_idx in 0..test_item_detail_arc.len() {
            let sender_clone = sender.clone();
            let file_detail_map_arc_clone = file_detail_map_arc.clone(); // 移动 Arc 克隆到这里
            let test_item_detail_arc_clone = test_item_detail_arc.clone(); // 移动 Arc 克隆到这里
            let semaphore_clone = semaphore.clone();

            let task = tokio::spawn(async move {
                // 获取信号量许可
                let _permit = semaphore_clone.acquire().await.unwrap();

                let test_item_detail_batch = &test_item_detail_arc_clone[batch_idx];
                log::info!("开始处理批次{}写入ClickHouse，数据量: {}", batch_idx, test_item_detail_batch.len());

                // 处理批次中的每个项目
                for item in test_item_detail_batch.iter() {
                    let file_detail = file_detail_map_arc_clone.get(&(item.FILE_ID.unwrap() as u64)).unwrap();
                    let test_item_detail_row = TestItemDetailRow::new(item, file_detail);

                    sender_clone.send(Some(test_item_detail_row)).await?;
                }

                log::info!("完成批次{}写入ClickHouse", batch_idx);
                Ok::<(), Box<dyn Error + Send + Sync>>(())
            });
            tasks.push(task);
        }

        // 等待所有任务完成
        for task in tasks {
            task.await??;
        }

        // 发送结束消息
        sender.send(None).await?;

        // 等待处理器完成
        processor_handle.await?;

        // 输出最终指标
        log::info!("ClickHouse写入最终指标:\n {}", metrics.snapshot().format_summary());

        // 从Arc中提取原始数据返回
        let file_detail_map = Arc::try_unwrap(file_detail_map_arc).map_err(|_| "Failed to unwrap Arc<HashMap>")?;
        let test_item_detail =
            Arc::try_unwrap(test_item_detail_arc).map_err(|_| "Failed to unwrap Arc<Vec<Vec<SubTestItemDetail>>>")?;

        Ok((test_item_detail, file_detail_map))
    }

    /// Build die test info map from die details for broadcasting
    /// This method creates the broadcast map used in the calculation
    ///
    /// Corresponds to: CpDwdTestItemService.scala:43
    /// val dieTestInfoMap = sc.broadcast(dieDetail.map(buildDieTestInfo).rdd.distinct().collect.toMap)
    fn build_die_test_info_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<DieKey, DieTestInfo>, Box<dyn Error + Send + Sync>> {
        DwdService::build_die_test_info_broadcast_map(die_details).map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string())) as Box<dyn Error + Send + Sync>
        })
    }

    /// Build file detail broadcast map from die details using FileDetailService
    /// This method creates the file detail map used for merging with test item details
    ///
    /// Corresponds to: CpDwdTestItemService.scala:45
    /// val fileDetailMap = FileDetailService().broadcastFileDetail(spark, dieDetail)
    fn build_file_detail_broadcast_map(
        &self,
        die_details: &[DieDetailParquet],
    ) -> Result<HashMap<u64, FileDetail>, Box<dyn Error + Send + Sync>> {
        let file_detail_service = common::dto::dwd::file_detail::FileDetailService::new();
        file_detail_service.broadcast_file_detail(die_details).map_err(|e| {
            Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string())) as Box<dyn Error + Send + Sync>
        })
    }

    /// Get test item detail result partition count
    pub fn get_test_item_detail_result_partition(&self) -> i32 {
        self.test_item_detail_result_partition
    }
}
