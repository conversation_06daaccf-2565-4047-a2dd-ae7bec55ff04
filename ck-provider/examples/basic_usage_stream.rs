use ck_provider::{
    AsyncCkChannel, CkConfig, CkProvider, CkProviderImpl, CkStreamProcessorBuilder, StreamConfig, StreamMetrics,
};
use clickhouse::Row;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::Duration;
use tokio::time::sleep;
use tracing::Level;
use tracing_subscriber::fmt::format::FmtSpan;

#[derive(Row, Serialize, Deserialize, Debug, Clone)]
struct User {
    id: u32,
    name: String,
    age: u8,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    tracing_subscriber::fmt()
        .with_max_level(Level::INFO)
        .with_span_events(FmtSpan::FULL)
        .with_file(true)
        .with_line_number(true)
        .init();

    // 配置ClickHouse连接 - 使用默认配置并自定义需要的字段
    let config = CkConfig {
        url: "http://192.168.2.26:8123".to_string(),
        username: "admin".to_string(),
        password: "admin@ck@Guwave".to_string(),
        database: "test".to_string(),
        timeout: Duration::from_secs(30),
        batch_size: 1000,
        compression: true,
        ..Default::default() // 使用默认值填充其他字段
    };

    // 创建ClickHouse Provider
    let provider = CkProviderImpl::new(config);

    // 创建测试表
    provider.execute("DROP TABLE IF EXISTS users_stream").await?;
    provider
        .execute("CREATE TABLE users_stream (id UInt32, name String, age UInt8) ENGINE = Memory")
        .await?;

    println!("创建表成功");

    // 配置流式处理参数
    let stream_config = StreamConfig::default()
        .with_buffer_size(100)
        .with_batch_size(50)
        .with_flush_interval(Duration::from_millis(500))
        .with_max_retries(3)
        .with_backpressure_timeout(Duration::from_secs(5))
        // 并行写入配置
        .with_parallel_flush(true)
        .with_max_concurrent_flushes(2);

    // 创建指标收集器
    let metrics = Arc::new(StreamMetrics::new());

    // 创建流式通道
    let (sender, receiver) = AsyncCkChannel::new::<User>(stream_config.clone(), metrics.clone());

    // 创建流处理器
    let mut processor = CkStreamProcessorBuilder::new()
        .with_receiver(receiver)
        .with_provider(provider.clone())
        .with_metrics(metrics.clone())
        .with_config(stream_config)
        .with_table_name("users_stream".to_string())
        .build()?;

    // 启动流处理器任务
    let processor_handle = tokio::spawn(async move {
        if let Err(e) = processor.start().await {
            eprintln!("流处理器错误: {:?}", e);
        }
    });

    // 发送数据到流中
    println!("开始发送数据到流...");

    // 发送数据
    for i in 1..=505 {
        let user = User { id: i, name: format!("用户{}", i), age: (20 + (i % 50)) as u8 };
        sender.send(Some(user)).await?;
        println!("已发送用户 {} 到流", i);
    }
    // 发送结束标志
    sender.send(None).await?;
    println!("已发送结束标记到流");

    // 显示中间指标
    println!("\n当前指标:");
    println!("{}", metrics.snapshot().format_summary());

    // 关闭发送器
    sender.close();

    // 等待处理器完成
    tokio::time::timeout(Duration::from_secs(5), processor_handle).await.map_err(|_| "写数据到ck超时")??;

    // 最终指标
    println!("\n最终指标:");
    println!("{}", metrics.snapshot().format_summary());

    // 查询数据验证
    let result: Vec<User> = provider.query("SELECT * FROM users_stream ORDER BY id").await?;
    println!("查询到 {} 条记录", result.len());
    println!("前5条记录: {:?}", &result[..std::cmp::min(5, result.len())]);
    println!("后5条记录: {:?}", &result[std::cmp::max(result.len(), 5) - 5..]);

    // 清理
    provider.execute("DROP TABLE IF EXISTS users_stream").await?;
    println!("清理完成");

    Ok(())
}
