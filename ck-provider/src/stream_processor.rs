use crate::config::StreamConfig;
use crate::error::CkProviderError;
use crate::metrics::StreamMetrics;
use crate::provider::{CkProvider, CkProviderImpl};
use clickhouse::Row;
use serde::Serialize;
use std::sync::Arc;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::time::Duration;
use tokio::select;
use tokio::sync::{mpsc, Semaphore};
use tokio::time::{sleep, Instant as TokioInstant};

/// Stream processor for consuming channel data and writing to ClickHouse
pub struct CkStreamProcessor<T> {
    receiver: mpsc::Receiver<Option<T>>,
    provider: Arc<CkProviderImpl>,
    metrics: Arc<StreamMetrics>,
    config: StreamConfig,
    table_name: String,
    batch_buffer: Vec<T>,
    last_flush_time: TokioInstant,
    is_running: Arc<std::sync::atomic::AtomicBool>,
    // 并行flush相关字段
    flush_semaphore: Arc<Semaphore>,
    pending_flushes: Arc<AtomicUsize>,
}

impl<T> CkStreamProcessor<T>
where
    T: Row + Serialize + Send + Sync + 'static,
{
    /// Create a new stream processor
    pub fn new(
        receiver: mpsc::Receiver<Option<T>>,
        provider: CkProviderImpl,
        metrics: Arc<StreamMetrics>,
        config: StreamConfig,
        table_name: String,
    ) -> Self {
        let batch_capacity = config.batch_size;
        // 使用配置中的并发flush数量
        let max_concurrent_flushes = if config.enable_parallel_flush {
            config.max_concurrent_flushes
        } else {
            1 // 禁用并行flush时只允许1个并发
        };
        
        Self {
            receiver,
            provider: Arc::new(provider),
            metrics,
            config,
            table_name,
            batch_buffer: Vec::with_capacity(batch_capacity),
            last_flush_time: TokioInstant::now(),
            is_running: Arc::new(std::sync::atomic::AtomicBool::new(false)),
            flush_semaphore: Arc::new(Semaphore::new(max_concurrent_flushes)),
            pending_flushes: Arc::new(AtomicUsize::new(0)),
        }
    }

    /// Start processing the stream
    pub async fn start(&mut self) -> Result<(), CkProviderError> {
        self.is_running.store(true, std::sync::atomic::Ordering::Relaxed);
        log::info!("Starting CkStreamProcessor for table: {}", self.table_name);

        let mut flush_interval = tokio::time::interval(self.config.flush_interval);
        flush_interval.set_missed_tick_behavior(tokio::time::MissedTickBehavior::Skip);

        loop {
            select! {
                // Receive data from channel
                item = self.receiver.recv() => {
                    match item {
                        Some(data) => {
                            match data {
                                Some(data) => {
                                   self.add_to_batch(data).await?;
                                }
                                None => {
                                       // Channel closed, flush remaining data and exit
                                       log::info!("Channel closed, flushing remaining data");
                                       if self.config.enable_parallel_flush {
                                           self.flush_batch_parallel().await?;
                                           // 等待所有pending的flush完成
                                           self.wait_for_pending_flushes().await;
                                       } else {
                                           self.flush_batch().await?;
                                       }
                                       break;
                                }
                            }
                        }
                        None => {
                        // Channel closed, flush remaining data and exit
                        log::info!("Channel closed, flushing remaining data");
                        if self.config.enable_parallel_flush {
                            self.flush_batch_parallel().await?;
                            // 等待所有pending的flush完成
                            self.wait_for_pending_flushes().await;
                        } else {
                            self.flush_batch().await?;
                        }
                        break;
                        }
                    }
                }

                // Periodic flush based on time interval
                _ = flush_interval.tick() => {
                    if self.should_flush_by_time() {
                        if self.config.enable_parallel_flush {
                            self.flush_batch_parallel().await?;
                        } else {
                            self.flush_batch().await?;
                        }
                    }
                }
            }
        }

        self.is_running.store(false, std::sync::atomic::Ordering::Relaxed);
        log::info!("CkStreamProcessor stopped for table: {}", self.table_name);
        Ok(())
    }

    /// Stop the processor gracefully
    pub fn stop(&self) {
        self.is_running.store(false, std::sync::atomic::Ordering::Relaxed);
    }

    /// Check if the processor is running
    pub fn is_running(&self) -> bool {
        self.is_running.load(std::sync::atomic::Ordering::Relaxed)
    }

    /// Add item to batch and flush if necessary
    async fn add_to_batch(&mut self, item: T) -> Result<(), CkProviderError> {
        self.batch_buffer.push(item);
        self.metrics.set_queue_size(self.batch_buffer.len());

        // Flush if batch is full
        if self.batch_buffer.len() >= self.config.batch_size {
            if self.config.enable_parallel_flush {
                self.flush_batch_parallel().await?;
            } else {
                self.flush_batch().await?;
            }
        }

        Ok(())
    }

    /// Check if batch should be flushed based on time
    fn should_flush_by_time(&self) -> bool {
        !self.batch_buffer.is_empty() && self.last_flush_time.elapsed() >= self.config.flush_interval
    }

    /// Flush the current batch to ClickHouse with parallel processing
    async fn flush_batch_parallel(&mut self) -> Result<(), CkProviderError> {
        if self.batch_buffer.is_empty() {
            return Ok(());
        }

        let batch_size = self.batch_buffer.len();
        log::debug!("Parallel flushing batch of {} items to table: {}", batch_size, self.table_name);

        // 移动batch数据，避免借用冲突
        let batch_data = std::mem::take(&mut self.batch_buffer);
        self.last_flush_time = TokioInstant::now();
        self.metrics.set_queue_size(0);

        // 启动并行flush任务
        let provider = self.provider.clone();
        let metrics = self.metrics.clone();
        let table_name = self.table_name.clone();
        let config = self.config.clone();
        let semaphore = self.flush_semaphore.clone();
        let pending_flushes = self.pending_flushes.clone();

        // 增加pending计数
        pending_flushes.fetch_add(1, Ordering::Relaxed);

        tokio::spawn(async move {
            let _permit = semaphore.acquire().await.unwrap();
            let start_time = TokioInstant::now();
            
            // 执行实际的写入操作
            let result = Self::write_batch_with_retry(
                &provider,
                &table_name,
                batch_data,
                &config,
                &metrics,
            ).await;

            match result {
                Ok(()) => {
                    let elapsed = start_time.elapsed();
                    metrics.increment_items_processed(batch_size as u64);
                    metrics.increment_batches_processed();
                    // 使用专门的批次处理时间统计，避免与单个item处理时间混淆
                    metrics.add_batch_process_time(elapsed);
                    log::debug!("Parallel batch write succeeded for {} items in {:?}", batch_size, elapsed);
                }
                Err(e) => {
                    metrics.increment_errors();
                    log::error!("Parallel batch write failed: {}", e);
                }
            }

            // 减少pending计数
            pending_flushes.fetch_sub(1, Ordering::Relaxed);
        });

        Ok(())
    }

    /// 等待所有pending的flush完成
    async fn wait_for_pending_flushes(&self) {
        while self.pending_flushes.load(Ordering::Relaxed) > 0 {
            tokio::time::sleep(Duration::from_millis(10)).await;
        }
        log::info!("All pending flushes completed");
    }

    /// 带重试的批量写入（静态方法，用于并行任务）
    async fn write_batch_with_retry(
        provider: &CkProviderImpl,
        table_name: &str,
        batch_data: Vec<T>,
        config: &StreamConfig,
        metrics: &StreamMetrics,
    ) -> Result<(), CkProviderError> {
        let mut retry_count = 0;
        let mut last_error = None;

        while retry_count <= config.max_retries {
            match provider.insert(table_name, &batch_data).await {
                Ok(()) => {
                    if retry_count > 0 {
                        log::info!("Parallel batch write succeeded after {} retries", retry_count);
                    }
                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    retry_count += 1;
                    metrics.increment_retries();

                    if retry_count <= config.max_retries {
                        let delay = Self::calculate_retry_delay_static(retry_count);
                        log::warn!(
                            "Parallel batch write failed (attempt {}/{}), retrying in {:?}: {}",
                            retry_count,
                            config.max_retries + 1,
                            delay,
                            last_error.as_ref().unwrap()
                        );
                        sleep(delay).await;
                    }
                }
            }
        }

        // All retries exhausted
        let error = last_error
            .unwrap_or_else(|| CkProviderError::BatchError("Unknown error during parallel batch processing".to_string()));

        log::error!("Failed to write parallel batch after {} attempts: {}", config.max_retries + 1, error);
        Err(error)
    }

    /// Flush the current batch to ClickHouse with retry logic (保留原有的串行方法作为备用)
    async fn flush_batch(&mut self) -> Result<(), CkProviderError> {
        if self.batch_buffer.is_empty() {
            return Ok(());
        }

        let batch_size = self.batch_buffer.len();
        let start_time = TokioInstant::now();

        log::debug!("Flushing batch of {} items to table: {}", batch_size, self.table_name);

        // Attempt to write with retries
        let mut retry_count = 0;
        let mut last_error = None;

        while retry_count <= self.config.max_retries {
            match self.write_batch_to_clickhouse().await {
                Ok(()) => {
                    // Success - update metrics and clear buffer
                    self.metrics.increment_items_processed(batch_size as u64);
                    self.metrics.increment_batches_processed();
                    self.metrics.add_process_time(start_time.elapsed());
                    self.batch_buffer.clear();
                    self.last_flush_time = TokioInstant::now();
                    self.metrics.set_queue_size(0);

                    if retry_count > 0 {
                        log::info!("Batch write succeeded after {} retries", retry_count);
                    }

                    return Ok(());
                }
                Err(e) => {
                    last_error = Some(e);
                    retry_count += 1;
                    self.metrics.increment_retries();

                    if retry_count <= self.config.max_retries {
                        let delay = self.calculate_retry_delay(retry_count);
                        log::warn!(
                            "Batch write failed (attempt {}/{}), retrying in {:?}: {}",
                            retry_count,
                            self.config.max_retries + 1,
                            delay,
                            last_error.as_ref().unwrap()
                        );
                        sleep(delay).await;
                    }
                }
            }
        }

        // All retries exhausted
        self.metrics.increment_errors();
        let error = last_error
            .unwrap_or_else(|| CkProviderError::BatchError("Unknown error during batch processing".to_string()));

        log::error!("Failed to write batch after {} attempts: {}", self.config.max_retries + 1, error);

        Err(error)
    }

    /// Write the current batch to ClickHouse
    async fn write_batch_to_clickhouse(&self) -> Result<(), CkProviderError> {
        if self.batch_buffer.is_empty() {
            return Ok(());
        }

        // Use the existing provider's insert method
        self.provider.insert(&self.table_name, &self.batch_buffer).await
    }

    /// Calculate retry delay with exponential backoff
    fn calculate_retry_delay(&self, retry_count: u32) -> Duration {
        Self::calculate_retry_delay_static(retry_count)
    }

    /// Static version of calculate_retry_delay for use in parallel tasks
    fn calculate_retry_delay_static(retry_count: u32) -> Duration {
        let base_delay = Duration::from_millis(100);
        let max_delay = Duration::from_secs(30);

        let delay = base_delay * (2_u32.pow(retry_count.saturating_sub(1)));
        std::cmp::min(delay, max_delay)
    }

    /// Get current batch size
    pub fn current_batch_size(&self) -> usize {
        self.batch_buffer.len()
    }

    /// Get metrics reference
    pub fn metrics(&self) -> &Arc<StreamMetrics> {
        &self.metrics
    }

    /// Get configuration reference
    pub fn config(&self) -> &StreamConfig {
        &self.config
    }

    /// Get table name
    pub fn table_name(&self) -> &str {
        &self.table_name
    }

    /// Get number of pending flush operations
    pub fn pending_flushes(&self) -> usize {
        self.pending_flushes.load(Ordering::Relaxed)
    }

    /// Check if there are any pending flush operations
    pub fn has_pending_flushes(&self) -> bool {
        self.pending_flushes() > 0
    }
}

/// Builder for creating CkStreamProcessor instances
pub struct CkStreamProcessorBuilder<T> {
    receiver: Option<mpsc::Receiver<Option<T>>>,
    provider: Option<CkProviderImpl>,
    metrics: Option<Arc<StreamMetrics>>,
    config: Option<StreamConfig>,
    table_name: Option<String>,
}

impl<T> Default for CkStreamProcessorBuilder<T>
where
    T: Row + Serialize + Send + Sync + 'static,
{
    fn default() -> Self {
        Self::new()
    }
}

impl<T> CkStreamProcessorBuilder<T>
where
    T: Row + Serialize + Send + Sync + 'static,
{
    /// Create a new builder
    pub fn new() -> Self {
        Self { receiver: None, provider: None, metrics: None, config: None, table_name: None }
    }

    /// Set the receiver
    pub fn with_receiver(mut self, receiver: mpsc::Receiver<Option<T>>) -> Self {
        self.receiver = Some(receiver);
        self
    }

    /// Set the provider
    pub fn with_provider(mut self, provider: CkProviderImpl) -> Self {
        self.provider = Some(provider);
        self
    }

    /// Set the metrics
    pub fn with_metrics(mut self, metrics: Arc<StreamMetrics>) -> Self {
        self.metrics = Some(metrics);
        self
    }

    /// Set the configuration
    pub fn with_config(mut self, config: StreamConfig) -> Self {
        self.config = Some(config);
        self
    }

    /// Set the table name
    pub fn with_table_name(mut self, table_name: String) -> Self {
        self.table_name = Some(table_name);
        self
    }

    /// Build the processor
    pub fn build(self) -> Result<CkStreamProcessor<T>, CkProviderError> {
        let receiver = self
            .receiver
            .ok_or_else(|| CkProviderError::StreamConfigError("Receiver is required".to_string()))?;

        let provider = self
            .provider
            .ok_or_else(|| CkProviderError::StreamConfigError("Provider is required".to_string()))?;

        let metrics = self
            .metrics
            .ok_or_else(|| CkProviderError::StreamConfigError("Metrics is required".to_string()))?;

        let config = self.config.unwrap_or_default();

        let table_name = self
            .table_name
            .ok_or_else(|| CkProviderError::StreamConfigError("Table name is required".to_string()))?;

        Ok(CkStreamProcessor::new(receiver, provider, metrics, config, table_name))
    }
}
