use crate::config::StreamConfig;
use crate::error::CkProviderError;
use crate::metrics::StreamMetrics;
use std::sync::Arc;
use std::time::Instant;
use tokio::sync::mpsc;
use tokio::time::timeout;

/// Async sender for ClickHouse streaming operations with backpressure control
#[derive(Debug)]
pub struct AsyncCkSender<T> {
    sender: mpsc::Sender<Option<T>>,
    metrics: Arc<StreamMetrics>,
    config: StreamConfig,
    is_closed: std::sync::atomic::AtomicBool, // Individual close state per sender instance
}

impl<T> Clone for AsyncCkSender<T> {
    fn clone(&self) -> Self {
        Self {
            sender: self.sender.clone(),
            metrics: Arc::clone(&self.metrics),
            config: self.config.clone(),
            is_closed: std::sync::atomic::AtomicBool::new(false), // Each clone starts as not closed
        }
    }
}

impl<T> AsyncCkSender<T>
where
    T: Send + 'static,
{
    /// Create a new AsyncCkSender with the given channel sender
    pub fn new(sender: mpsc::Sender<Option<T>>, metrics: Arc<StreamMetrics>, config: StreamConfig) -> Self {
        Self { sender, metrics, config, is_closed: std::sync::atomic::AtomicBool::new(false) }
    }

    /// Send a single item with automatic backpressure handling
    pub async fn send(&self, item: Option<T>) -> Result<(), CkProviderError> {
        if self.is_closed() {
            return Err(CkProviderError::StreamClosed("Sender is closed".to_string()));
        }

        let start_time = Instant::now();
        let is_data = item.is_some();

        // Try to send with timeout for backpressure control
        let send_result = timeout(self.config.backpressure_timeout, self.sender.send(item)).await;

        match send_result {
            Ok(Ok(())) => {
                // Successfully sent
                if is_data {
                    self.metrics.increment_items_sent(1);
                }
                self.metrics.add_send_time(start_time.elapsed());
                Ok(())
            }
            Ok(Err(_)) => {
                // Channel is closed
                self.mark_closed();
                Err(CkProviderError::StreamClosed("Channel is closed".to_string()))
            }
            Err(_) => {
                // Timeout occurred - backpressure
                self.metrics.increment_backpressure_events();
                Err(CkProviderError::BackpressureTimeout(format!(
                    "Send timeout after {:?}",
                    self.config.backpressure_timeout
                )))
            }
        }
    }

    /// Try to send an item without waiting (non-blocking)
    pub fn try_send(&self, item: Option<T>) -> Result<(), CkProviderError> {
        if self.is_closed() {
            return Err(CkProviderError::StreamClosed("Sender is closed".to_string()));
        }
        let is_data = item.is_some();

        match self.sender.try_send(item) {
            Ok(()) => {
                if is_data {
                    self.metrics.increment_items_sent(1);
                }
                Ok(())
            }
            Err(mpsc::error::TrySendError::Full(_)) => {
                self.metrics.increment_backpressure_events();
                Err(CkProviderError::BackpressureTimeout("Channel is full".to_string()))
            }
            Err(mpsc::error::TrySendError::Closed(_)) => {
                self.mark_closed();
                Err(CkProviderError::StreamClosed("Channel is closed".to_string()))
            }
        }
    }

    /// Close the sender gracefully
    pub fn close(&self) {
        self.mark_closed();
        // Note: We don't close the actual sender here because other clones might still be using it
        // The sender will be closed when all clones are dropped
    }

    /// Close the sender and wait for all data to be processed
    /// This is a convenience method that closes the sender and returns immediately
    /// The caller should use the processor's wait_for_completion method to wait for processing to finish
    pub fn close_and_signal_completion(&self) {
        self.close();
        // The actual waiting should be done on the processor side
    }

    /// Check if the sender is closed
    pub fn is_closed(&self) -> bool {
        self.is_closed.load(std::sync::atomic::Ordering::Relaxed) || self.sender.is_closed()
    }

    /// Get the current capacity of the channel
    pub fn capacity(&self) -> usize {
        self.sender.capacity()
    }

    /// Get a reference to the metrics
    pub fn metrics(&self) -> &Arc<StreamMetrics> {
        &self.metrics
    }

    /// Get a reference to the configuration
    pub fn config(&self) -> &StreamConfig {
        &self.config
    }

    /// Mark the sender as closed
    fn mark_closed(&self) {
        self.is_closed.store(true, std::sync::atomic::Ordering::Relaxed);
    }
}

/// Factory for creating AsyncCkSender and receiver pairs
pub struct AsyncCkChannel;

impl AsyncCkChannel {
    /// Create a new async channel pair for ClickHouse streaming
    pub fn new<T>(config: StreamConfig, metrics: Arc<StreamMetrics>) -> (AsyncCkSender<T>, mpsc::Receiver<Option<T>>)
    where
        T: Send + 'static,
    {
        let (sender, receiver) = mpsc::channel(config.buffer_size);
        let async_sender = AsyncCkSender::new(sender, metrics, config);
        (async_sender, receiver)
    }
}
