use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

/// Metrics for monitoring async streaming operations
#[derive(Debug)]
pub struct StreamMetrics {
    // Counters for operations
    pub items_sent: AtomicU64,
    pub items_processed: AtomicU64,
    pub batches_sent: AtomicU64,
    pub batches_processed: AtomicU64,
    pub errors_count: AtomicU64,
    pub retries_count: AtomicU64,

    // Timing metrics (in microseconds for precision)
    pub total_send_time_micros: AtomicU64,
    pub total_process_time_micros: AtomicU64,
    pub total_wait_time_micros: AtomicU64,
    
    // Batch-level timing for parallel operations
    pub total_batch_process_time_micros: AtomicU64,

    // Current state metrics
    pub active_connections: AtomicUsize,
    pub queue_size: AtomicUsize,
    pub backpressure_events: AtomicU64,

    // Timestamps
    pub start_time: SystemTime,
    pub last_activity: AtomicU64, // Unix timestamp in seconds
}

impl Default for StreamMetrics {
    fn default() -> Self {
        Self::new()
    }
}

impl StreamMetrics {
    /// Create a new StreamMetrics instance
    pub fn new() -> Self {
        let now = SystemTime::now();
        let timestamp = now.duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();

        Self {
            items_sent: AtomicU64::new(0),
            items_processed: AtomicU64::new(0),
            batches_sent: AtomicU64::new(0),
            batches_processed: AtomicU64::new(0),
            errors_count: AtomicU64::new(0),
            retries_count: AtomicU64::new(0),
            total_send_time_micros: AtomicU64::new(0),
            total_process_time_micros: AtomicU64::new(0),
            total_wait_time_micros: AtomicU64::new(0),
            total_batch_process_time_micros: AtomicU64::new(0),
            active_connections: AtomicUsize::new(0),
            queue_size: AtomicUsize::new(0),
            backpressure_events: AtomicU64::new(0),
            start_time: now,
            last_activity: AtomicU64::new(timestamp),
        }
    }

    /// Increment items sent counter
    pub fn increment_items_sent(&self, count: u64) {
        self.items_sent.fetch_add(count, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Increment items processed counter
    pub fn increment_items_processed(&self, count: u64) {
        self.items_processed.fetch_add(count, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Increment batches sent counter
    pub fn increment_batches_sent(&self) {
        self.batches_sent.fetch_add(1, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Increment batches processed counter
    pub fn increment_batches_processed(&self) {
        self.batches_processed.fetch_add(1, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Increment error counter
    pub fn increment_errors(&self) {
        self.errors_count.fetch_add(1, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Increment retry counter
    pub fn increment_retries(&self) {
        self.retries_count.fetch_add(1, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Increment backpressure events counter
    pub fn increment_backpressure_events(&self) {
        self.backpressure_events.fetch_add(1, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Add send time measurement
    pub fn add_send_time(&self, duration: Duration) {
        self.total_send_time_micros
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Add process time measurement
    pub fn add_process_time(&self, duration: Duration) {
        self.total_process_time_micros
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Add wait time measurement
    pub fn add_wait_time(&self, duration: Duration) {
        self.total_wait_time_micros
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Add batch process time measurement (for parallel operations)
    pub fn add_batch_process_time(&self, duration: Duration) {
        self.total_batch_process_time_micros
            .fetch_add(duration.as_micros() as u64, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Set active connections count
    pub fn set_active_connections(&self, count: usize) {
        self.active_connections.store(count, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Set queue size
    pub fn set_queue_size(&self, size: usize) {
        self.queue_size.store(size, Ordering::Relaxed);
        self.update_last_activity();
    }

    /// Get items sent count
    pub fn get_items_sent(&self) -> u64 {
        self.items_sent.load(Ordering::Relaxed)
    }

    /// Get items processed count
    pub fn get_items_processed(&self) -> u64 {
        self.items_processed.load(Ordering::Relaxed)
    }

    /// Get batches sent count
    pub fn get_batches_sent(&self) -> u64 {
        self.batches_sent.load(Ordering::Relaxed)
    }

    /// Get batches processed count
    pub fn get_batches_processed(&self) -> u64 {
        self.batches_processed.load(Ordering::Relaxed)
    }

    /// Get error count
    pub fn get_errors_count(&self) -> u64 {
        self.errors_count.load(Ordering::Relaxed)
    }

    /// Get retry count
    pub fn get_retries_count(&self) -> u64 {
        self.retries_count.load(Ordering::Relaxed)
    }

    /// Get backpressure events count
    pub fn get_backpressure_events(&self) -> u64 {
        self.backpressure_events.load(Ordering::Relaxed)
    }

    /// Get active connections count
    pub fn get_active_connections(&self) -> usize {
        self.active_connections.load(Ordering::Relaxed)
    }

    /// Get current queue size
    pub fn get_queue_size(&self) -> usize {
        self.queue_size.load(Ordering::Relaxed)
    }

    /// Calculate average send time per item in microseconds
    pub fn get_average_send_time_micros(&self) -> f64 {
        let total_time = self.total_send_time_micros.load(Ordering::Relaxed);
        let items_sent = self.items_sent.load(Ordering::Relaxed);

        if items_sent == 0 {
            0.0
        } else {
            total_time as f64 / items_sent as f64
        }
    }

    /// Calculate average process time per item in microseconds
    pub fn get_average_process_time_micros(&self) -> f64 {
        let total_time = self.total_process_time_micros.load(Ordering::Relaxed);
        let items_processed = self.items_processed.load(Ordering::Relaxed);

        if items_processed == 0 {
            0.0
        } else {
            total_time as f64 / items_processed as f64
        }
    }

    /// Calculate average batch process time in microseconds (for parallel operations)
    pub fn get_average_batch_process_time_micros(&self) -> f64 {
        let total_time = self.total_batch_process_time_micros.load(Ordering::Relaxed);
        let batches_processed = self.batches_processed.load(Ordering::Relaxed);

        if batches_processed == 0 {
            0.0
        } else {
            total_time as f64 / batches_processed as f64
        }
    }

    /// Calculate average wait time per operation in microseconds
    pub fn get_average_wait_time_micros(&self) -> f64 {
        let total_time = self.total_wait_time_micros.load(Ordering::Relaxed);
        let operations = self.batches_sent.load(Ordering::Relaxed);

        if operations == 0 {
            0.0
        } else {
            total_time as f64 / operations as f64
        }
    }

    /// Calculate throughput in items per second
    pub fn get_throughput_items_per_second(&self) -> f64 {
        let elapsed = self.start_time.elapsed().unwrap_or_default();
        let items_processed = self.items_processed.load(Ordering::Relaxed);

        if elapsed.as_secs() == 0 {
            0.0
        } else {
            items_processed as f64 / elapsed.as_secs_f64()
        }
    }

    /// Calculate batch throughput in batches per second
    pub fn get_batch_throughput_per_second(&self) -> f64 {
        let elapsed = self.start_time.elapsed().unwrap_or_default();
        let batches_processed = self.batches_processed.load(Ordering::Relaxed);

        if elapsed.as_secs() == 0 {
            0.0
        } else {
            batches_processed as f64 / elapsed.as_secs_f64()
        }
    }

    /// Calculate error rate as percentage
    pub fn get_error_rate(&self) -> f64 {
        let errors = self.errors_count.load(Ordering::Relaxed);
        let total_operations = self.batches_sent.load(Ordering::Relaxed);

        if total_operations == 0 {
            0.0
        } else {
            (errors as f64 / total_operations as f64) * 100.0
        }
    }

    /// Get uptime in seconds
    pub fn get_uptime_seconds(&self) -> u64 {
        self.start_time.elapsed().unwrap_or_default().as_secs()
    }

    /// Get last activity timestamp
    pub fn get_last_activity_timestamp(&self) -> u64 {
        self.last_activity.load(Ordering::Relaxed)
    }

    /// Reset all metrics to zero
    pub fn reset(&self) {
        self.items_sent.store(0, Ordering::Relaxed);
        self.items_processed.store(0, Ordering::Relaxed);
        self.batches_sent.store(0, Ordering::Relaxed);
        self.batches_processed.store(0, Ordering::Relaxed);
        self.errors_count.store(0, Ordering::Relaxed);
        self.retries_count.store(0, Ordering::Relaxed);
        self.total_send_time_micros.store(0, Ordering::Relaxed);
        self.total_process_time_micros.store(0, Ordering::Relaxed);
        self.total_wait_time_micros.store(0, Ordering::Relaxed);
        self.total_batch_process_time_micros.store(0, Ordering::Relaxed);
        self.active_connections.store(0, Ordering::Relaxed);
        self.queue_size.store(0, Ordering::Relaxed);
        self.backpressure_events.store(0, Ordering::Relaxed);

        let timestamp = SystemTime::now().duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
        self.last_activity.store(timestamp, Ordering::Relaxed);
    }

    /// Create a snapshot of current metrics for reporting
    pub fn snapshot(&self) -> StreamMetricsSnapshot {
        StreamMetricsSnapshot {
            items_sent: self.get_items_sent(),
            items_processed: self.get_items_processed(),
            batches_sent: self.get_batches_sent(),
            batches_processed: self.get_batches_processed(),
            errors_count: self.get_errors_count(),
            retries_count: self.get_retries_count(),
            backpressure_events: self.get_backpressure_events(),
            active_connections: self.get_active_connections(),
            queue_size: self.get_queue_size(),
            average_send_time_micros: self.get_average_send_time_micros(),
            average_process_time_micros: self.get_average_process_time_micros(),
            average_wait_time_micros: self.get_average_wait_time_micros(),
            average_batch_process_time_micros: self.get_average_batch_process_time_micros(),
            throughput_items_per_second: self.get_throughput_items_per_second(),
            batch_throughput_per_second: self.get_batch_throughput_per_second(),
            error_rate: self.get_error_rate(),
            uptime_seconds: self.get_uptime_seconds(),
            last_activity_timestamp: self.get_last_activity_timestamp(),
        }
    }

    /// Update last activity timestamp
    fn update_last_activity(&self) {
        let timestamp = SystemTime::now().duration_since(UNIX_EPOCH).unwrap_or_default().as_secs();
        self.last_activity.store(timestamp, Ordering::Relaxed);
    }
}

/// Snapshot of StreamMetrics for reporting and serialization
#[derive(Debug, Clone, PartialEq)]
pub struct StreamMetricsSnapshot {
    pub items_sent: u64,
    pub items_processed: u64,
    pub batches_sent: u64,
    pub batches_processed: u64,
    pub errors_count: u64,
    pub retries_count: u64,
    pub backpressure_events: u64,
    pub active_connections: usize,
    pub queue_size: usize,
    pub average_send_time_micros: f64,
    pub average_process_time_micros: f64,
    pub average_wait_time_micros: f64,
    pub average_batch_process_time_micros: f64,
    pub throughput_items_per_second: f64,
    pub batch_throughput_per_second: f64,
    pub error_rate: f64,
    pub uptime_seconds: u64,
    pub last_activity_timestamp: u64,
}

impl StreamMetricsSnapshot {
    /// Format metrics as a human-readable string
    pub fn format_summary(&self) -> String {
        // 判断是否使用了并行处理模式（如果有batch timing数据）
        let has_batch_timing = self.average_batch_process_time_micros > 0.0;
        
        if has_batch_timing {
            format!(
                "StreamMetrics Summary:\n\
                 Items: sent={}, processed={}\n\
                 Batches: sent={}, processed={}\n\
                 Errors: count={}, retries={}, rate={:.2}%\n\
                 Throughput: {:.2} items/sec, {:.2} batches/sec\n\
                 Timing: send={:.2}μs, batch_process={:.2}μs, wait={:.2}μs\n\
                 State: connections={}, queue={}, backpressure={}",
                self.items_sent,
                self.items_processed,
                self.batches_sent,
                self.batches_processed,
                self.errors_count,
                self.retries_count,
                self.error_rate,
                self.throughput_items_per_second,
                self.batch_throughput_per_second,
                self.average_send_time_micros,
                self.average_batch_process_time_micros,
                self.average_wait_time_micros,
                self.active_connections,
                self.queue_size,
                self.backpressure_events
            )
        } else {
            format!(
                "StreamMetrics Summary:\n\
                 Items: sent={}, processed={}\n\
                 Batches: sent={}, processed={}\n\
                 Errors: count={}, retries={}, rate={:.2}%\n\
                 Throughput: {:.2} items/sec, {:.2} batches/sec\n\
                 Timing: send={:.2}μs, process={:.2}μs, wait={:.2}μs\n\
                 State: connections={}, queue={}, backpressure={}",
                self.items_sent,
                self.items_processed,
                self.batches_sent,
                self.batches_processed,
                self.errors_count,
                self.retries_count,
                self.error_rate,
                self.throughput_items_per_second,
                self.batch_throughput_per_second,
                self.average_send_time_micros,
                self.average_process_time_micros,
                self.average_wait_time_micros,
                self.active_connections,
                self.queue_size,
                self.backpressure_events
            )
        }
    }
}
